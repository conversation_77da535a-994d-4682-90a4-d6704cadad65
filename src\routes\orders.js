const express = require('express');
const authMiddleware = require('../middleware/auth');
const orderController = require('../controllers/orderController');
const { USER_ROLES } = require('../utils/constants');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Orders
 *   description: Order management operations (place, track, update, cancel)
 */

// Apply authentication middleware
router.use(authMiddleware.authenticate);

/**
 * @swagger
 * /orders:
 *   post:
 *     summary: Place a new order
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - restaurantId
 *               - items
 *               - deliveryAddress
 *               - paymentMethod
 *             properties:
 *               restaurantId:
 *                 type: string
 *                 example: "507f1f77bcf86cd799439011"
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     menuItemId:
 *                       type: string
 *                       example: "507f1f77bcf86cd799439012"
 *                     quantity:
 *                       type: integer
 *                       example: 2
 *                     variant:
 *                       type: string
 *                       example: "large"
 *                     addons:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                             example: "Extra Cheese"
 *                           quantity:
 *                             type: integer
 *                             example: 1
 *                     specialInstructions:
 *                       type: string
 *                       example: "No onions please"
 *               deliveryAddress:
 *                 $ref: '#/components/schemas/Address'
 *               paymentMethod:
 *                 type: string
 *                 enum: [cod, online]
 *                 example: "cod"
 *               couponCode:
 *                 type: string
 *                 example: "FIRST20"
 *               specialInstructions:
 *                 type: string
 *                 example: "Ring the bell twice"
 *     responses:
 *       201:
 *         description: Order placed successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         orderId:
 *                           type: string
 *                         orderNumber:
 *                           type: string
 *                           example: "ORD-20241201-1234"
 *                         totalAmount:
 *                           type: number
 *                           example: 535
 *                         estimatedDeliveryTime:
 *                           type: string
 *                           format: date-time
 *                         paymentDetails:
 *                           type: object
 *                           nullable: true
 *                           properties:
 *                             paymentUrl:
 *                               type: string
 *                             orderId:
 *                               type: string
 *       400:
 *         description: Validation error or restaurant not available
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Order placement (customers only)
router.post('/',
  authMiddleware.authorize([USER_ROLES.CUSTOMER]),
  orderController.placeOrder
);

/**
 * @swagger
 * /orders/{orderId}:
 *   get:
 *     summary: Get specific order details
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *         description: Order ID
 *     responses:
 *       200:
 *         description: Order details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Order'
 *       404:
 *         description: Order not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /orders/{orderId}/status:
 *   put:
 *     summary: Update order status
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *         description: Order ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [accepted, preparing, ready, picked_up, out_for_delivery, delivered, cancelled]
 *                 example: "accepted"
 *               estimatedTime:
 *                 type: integer
 *                 example: 25
 *                 description: Estimated preparation/delivery time in minutes
 *               reason:
 *                 type: string
 *                 example: "Order cancelled due to unavailable items"
 *                 description: Reason for status change (required for cancellation)
 *     responses:
 *       200:
 *         description: Order status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Order status updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Order'
 *       400:
 *         description: Invalid status transition
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

/**
 * @swagger
 * /orders/{orderId}:
 *   delete:
 *     summary: Cancel order
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *         description: Order ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *                 example: "Changed my mind"
 *                 description: Reason for cancellation
 *     responses:
 *       200:
 *         description: Order cancelled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Order cancelled successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     orderId:
 *                       type: string
 *                     refundAmount:
 *                       type: number
 *                     refundStatus:
 *                       type: string
 *       400:
 *         description: Order cannot be cancelled
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */

// Get specific order details
router.get('/:orderId', orderController.getOrder);

// Update order status (restaurant owners and delivery partners)
router.put('/:orderId/status',
  authMiddleware.authorize([USER_ROLES.RESTAURANT_OWNER, USER_ROLES.DELIVERY_PARTNER, USER_ROLES.FRANCHISE_ADMIN, USER_ROLES.SUPER_ADMIN]),
  orderController.updateOrderStatus
);

// Cancel order (customers only)
router.delete('/:orderId',
  authMiddleware.authorize([USER_ROLES.CUSTOMER]),
  orderController.cancelOrder
);

module.exports = router;
