const express = require('express');
const authMiddleware = require('../middleware/auth');
const restaurantController = require('../controllers/restaurantController');
const { USER_ROLES } = require('../utils/constants');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Restaurants
 *   description: Restaurant management endpoints
 */

// Apply authentication middleware
router.use(authMiddleware.authenticate);

/**
 * @swagger
 * /restaurants:
 *   post:
 *     summary: Create a new restaurant
 *     tags: [Restaurants]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - businessInfo
 *               - location
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Burger Palace"
 *               businessInfo:
 *                 type: object
 *                 properties:
 *                   cuisine:
 *                     type: array
 *                     items:
 *                       type: string
 *                     example: ["american", "fast_food"]
 *                   description:
 *                     type: string
 *                     example: "Best burgers in town"
 *                   businessType:
 *                     type: string
 *                     example: "restaurant"
 *               location:
 *                 type: object
 *                 properties:
 *                   address:
 *                     type: string
 *                     example: "Shop 123, ABC Mall, Mumbai"
 *                   coordinates:
 *                     type: array
 *                     items:
 *                       type: number
 *                     example: [72.8777, 19.0760]
 *     responses:
 *       201:
 *         description: Restaurant created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Restaurant created successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Restaurant'
 */

/**
 * @swagger
 * /restaurants/{restaurantId}:
 *   get:
 *     summary: Get restaurant details
 *     tags: [Restaurants]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *         description: Restaurant ID
 *     responses:
 *       200:
 *         description: Restaurant details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Restaurant'
 *   put:
 *     summary: Update restaurant details
 *     tags: [Restaurants]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *         description: Restaurant ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               businessInfo:
 *                 type: object
 *                 properties:
 *                   cuisine:
 *                     type: array
 *                     items:
 *                       type: string
 *                   description:
 *                     type: string
 *               location:
 *                 type: object
 *                 properties:
 *                   address:
 *                     type: string
 *                   coordinates:
 *                     type: array
 *                     items:
 *                       type: number
 *     responses:
 *       200:
 *         description: Restaurant updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Restaurant updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Restaurant'
 */

/**
 * @swagger
 * /restaurants/{restaurantId}/menu:
 *   get:
 *     summary: Get restaurant menu
 *     tags: [Restaurants]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *         description: Restaurant ID
 *     responses:
 *       200:
 *         description: Menu retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     menu:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           _id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           description:
 *                             type: string
 *                           price:
 *                             type: number
 *                           category:
 *                             type: string
 *                           isAvailable:
 *                             type: boolean
 *   put:
 *     summary: Update restaurant menu
 *     tags: [Restaurants]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *         description: Restaurant ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               menu:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     description:
 *                       type: string
 *                     price:
 *                       type: number
 *                     category:
 *                       type: string
 *                     isAvailable:
 *                       type: boolean
 *     responses:
 *       200:
 *         description: Menu updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Menu updated successfully"
 */

/**
 * @swagger
 * /restaurants/{restaurantId}/orders:
 *   get:
 *     summary: Get restaurant orders
 *     tags: [Restaurants]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *         description: Restaurant ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [placed, accepted, preparing, ready, picked_up]
 *         description: Filter orders by status
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter orders by date
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     orders:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Order'
 */

// Create restaurant (restaurant owners only)
router.post('/',
  authMiddleware.authorize([USER_ROLES.RESTAURANT_OWNER]),
  restaurantController.createRestaurant
);

// Get restaurant details
router.get('/:restaurantId', restaurantController.getRestaurant);

// Update restaurant (restaurant owners, franchise admins, super admins)
router.put('/:restaurantId',
  authMiddleware.authorize([USER_ROLES.RESTAURANT_OWNER, USER_ROLES.FRANCHISE_ADMIN, USER_ROLES.SUPER_ADMIN]),
  restaurantController.updateRestaurant
);

// Get restaurant menu (public access for customers)
router.get('/:restaurantId/menu', restaurantController.getMenu);

// Update restaurant menu (restaurant owners only)
router.put('/:restaurantId/menu',
  authMiddleware.authorize([USER_ROLES.RESTAURANT_OWNER]),
  restaurantController.updateMenu
);

// Get restaurant orders (restaurant owners only)
router.get('/:restaurantId/orders',
  authMiddleware.authorize([USER_ROLES.RESTAURANT_OWNER, USER_ROLES.FRANCHISE_ADMIN, USER_ROLES.SUPER_ADMIN]),
  restaurantController.getOrders
);

// Update operational status (restaurant owners only)
router.patch('/:restaurantId/status', 
  authMiddleware.authorize([USER_ROLES.RESTAURANT_OWNER]), 
  restaurantController.updateOperationalStatus
);

module.exports = router;
