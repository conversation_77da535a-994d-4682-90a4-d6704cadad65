const express = require('express');
const authMiddleware = require('../middleware/auth');
const userController = require('../controllers/userController');
const orderController = require('../controllers/orderController');
const { USER_ROLES } = require('../utils/constants');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: User
 *   description: Customer user operations (profile, restaurants, orders, addresses)
 */

// Apply authentication middleware
router.use(authMiddleware.authenticate);

/**
 * @swagger
 * /user/profile:
 *   get:
 *     summary: Get user profile
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *   put:
 *     summary: Update user profile
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               profile:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     example: "John Doe"
 *                   avatar:
 *                     type: string
 *                     example: "https://example.com/avatar.jpg"
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Profile updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// User profile routes
router.get('/profile', authMiddleware.authorize([USER_ROLES.CUSTOMER]), userController.getProfile);
router.put('/profile', authMiddleware.authorize([USER_ROLES.CUSTOMER]), userController.updateProfile);

/**
 * @swagger
 * /user/restaurants:
 *   get:
 *     summary: Discover restaurants with geospatial search
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of restaurants per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by restaurant name or cuisine
 *         example: "burger"
 *       - in: query
 *         name: cuisine
 *         schema:
 *           type: string
 *         description: Filter by cuisine types (comma-separated)
 *         example: "american,fast_food"
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [rating, deliveryTime, distance, popularity]
 *           default: rating
 *         description: Sort restaurants by criteria
 *       - in: query
 *         name: latitude
 *         schema:
 *           type: number
 *         description: User's latitude for location-based search
 *         example: 19.0760
 *       - in: query
 *         name: longitude
 *         schema:
 *           type: number
 *         description: User's longitude for location-based search
 *         example: 72.8777
 *       - in: query
 *         name: maxDistance
 *         schema:
 *           type: integer
 *           default: 10000
 *         description: Maximum distance in meters
 *     responses:
 *       200:
 *         description: Restaurants retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/Restaurant'
 *                           - type: object
 *                             properties:
 *                               distance:
 *                                 type: number
 *                                 example: 2.5
 *                                 description: Distance in kilometers
 *                               isCurrentlyOpen:
 *                                 type: boolean
 *                                 example: true
 *                               estimatedDeliveryTime:
 *                                 type: number
 *                                 example: 35
 *                                 description: Estimated delivery time in minutes
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         pages:
 *                           type: integer
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Restaurant discovery routes
router.get('/restaurants', authMiddleware.authorize([USER_ROLES.CUSTOMER]), userController.getRestaurants);

/**
 * @swagger
 * /user/orders:
 *   get:
 *     summary: Get user order history
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [placed, accepted, preparing, ready, picked_up, out_for_delivery, delivered, cancelled]
 *         description: Filter orders by status
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of orders to return
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     orders:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Order'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *   post:
 *     summary: Place a new order
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - restaurantId
 *               - items
 *               - deliveryAddress
 *               - paymentMethod
 *             properties:
 *               restaurantId:
 *                 type: string
 *                 example: "60d5ecb74b24a1234567890a"
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     menuItemId:
 *                       type: string
 *                     quantity:
 *                       type: integer
 *                     customizations:
 *                       type: array
 *                       items:
 *                         type: string
 *               deliveryAddress:
 *                 $ref: '#/components/schemas/Address'
 *               paymentMethod:
 *                 type: string
 *                 enum: [cash, online, wallet]
 *               couponCode:
 *                 type: string
 *               specialInstructions:
 *                 type: string
 *     responses:
 *       201:
 *         description: Order placed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Order placed successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     orderId:
 *                       type: string
 *                     orderNumber:
 *                       type: string
 *                     totalAmount:
 *                       type: number
 *                     estimatedDeliveryTime:
 *                       type: string
 *                       format: date-time
 */

/**
 * @swagger
 * /user/addresses:
 *   get:
 *     summary: Get user addresses
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Addresses retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     addresses:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Address'
 *   post:
 *     summary: Add new address
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Address'
 *     responses:
 *       201:
 *         description: Address added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Address added successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Address'
 */

// Order management routes
router.get('/orders', authMiddleware.authorize([USER_ROLES.CUSTOMER]), userController.getOrders);
router.post('/orders', authMiddleware.authorize([USER_ROLES.CUSTOMER]), orderController.placeOrder);

// Address management routes
router.get('/addresses', authMiddleware.authorize([USER_ROLES.CUSTOMER]), userController.getAddresses);
router.post('/addresses', authMiddleware.authorize([USER_ROLES.CUSTOMER]), userController.addAddress);

module.exports = router;
